import paho.mqtt.client as mqtt
import threading
import random
import time
import json
import mysql.connector  # 导入MySQL连接器
from mysql.connector import Error  # 导入错误处理

# --- MQTT配置信息 ---
MQTT_BROKER_IP = 'localhost'  # 如果此脚本和EMQX在同一台服务器, 使用localhost, 否则使用公网IP
MQTT_BROKER_PORT = 1883
MQTT_TIMEOUT = 60

# 数据上行主题 (STM32发布数据到此主题)
DATA_TOPIC = "stm32/data"
# 指令下行主题格式 (脚本发布指令到此主题, {client_id} 会被替换)
COMMAND_TOPIC_FORMAT = "stm32/command/{client_id}"

# --- MySQL数据库配置 ---
# !!! 请根据您自己的设置修改以下信息 !!!
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'm2joy',  # 您创建的数据库用户名
    'password': 'Liu041121@',  # 您为用户设置的密码
    'database': 'iot_data'  # 您创建的数据库名
}


class MqttGateway:
    """
    一个MQTT网关，负责订阅设备数据，存入MySQL数据库，并根据逻辑下发指令。
    """

    def __init__(self, broker_ip, port, timeout):
        """
        初始化网关
        """
        self.broker_ip = broker_ip
        self.broker_port = port
        self.timeout = timeout
        self.connected = False
        self.client = None

        # 初始化数据库
        self.db_conn = self.setup_database()

        # 启动MQTT客户端
        self.start_client()

    def setup_database(self):
        """
        设置并连接到MySQL数据库, 如果表不存在则创建。
        :return: 数据库连接对象
        """
        try:
            conn = mysql.connector.connect(**MYSQL_CONFIG)
            if conn.is_connected():
                print("Successfully connected to MySQL database.")
                cursor = conn.cursor()
                # 创建一个表来存储传感器数据，如果它还不存在的话
                # 注意MySQL的AUTO_INCREMENT语法和数据类型
                cursor.execute('''
                               CREATE TABLE IF NOT EXISTS sensor_readings
                               (
                                   id
                                   INT
                                   AUTO_INCREMENT
                                   PRIMARY
                                   KEY,
                                   client_id
                                   VARCHAR
                               (
                                   255
                               ) NOT NULL,
                                   temperature DECIMAL
                               (
                                   5,
                                   2
                               ) NOT NULL,
                                   timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                                   );
                               ''')
                conn.commit()
                cursor.close()
                print("Table 'sensor_readings' is ready.")
                return conn
        except Error as e:
            print(f"Database error: {e}")
            return None

    def start_client(self):
        """
        配置并启动Paho MQTT客户端。
        """
        client_name = f"mqtt_gateway_{random.randint(1000, 9999)}"
        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION1, client_name)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message

        try:
            self.client.connect(self.broker_ip, self.broker_port, self.timeout)
            self.client.loop_start()
        except Exception as e:
            print(f"Failed to connect to MQTT broker: {e}")

    def on_connect(self, client, userdata, flags, rc):
        """
        连接到Broker时的回调函数。
        """
        if rc == 0:
            print("Successfully connected to MQTT Broker!")
            self.connected = True
            client.subscribe(DATA_TOPIC)
            print(f"Subscribed to topic: {DATA_TOPIC}")
        else:
            print(f"Failed to connect, return code {rc}\n")
            self.connected = False

    def on_message(self, client, userdata, msg):
        """
        当从订阅的主题接收到消息时的核心处理函数。
        """
        try:
            payload_str = msg.payload.decode('utf-8')
            print(f"Received message from topic '{msg.topic}': {payload_str}")

            data = json.loads(payload_str)
            client_id = data.get('client_id')
            temperature = data.get('temperature')

            if client_id is None or temperature is None:
                print("Warning: Received data is missing 'client_id' or 'temperature'.")
                return

            self.save_to_db(client_id, temperature)

            if float(temperature) > 30.0:
                print(f"ALERT: Temperature ({temperature}°C) from {client_id} exceeds threshold!")
                self.publish_command(client_id, "open_fan")

        except json.JSONDecodeError:
            print(f"Error: Failed to decode JSON from payload: {msg.payload}")
        except Exception as e:
            print(f"An error occurred in on_message: {e}")

    def save_to_db(self, client_id, temperature):
        """
        将数据保存到MySQL数据库。
        """
        if self.db_conn is None or not self.db_conn.is_connected():
            print("Cannot save to database, attempting to reconnect...")
            self.db_conn = self.setup_database()
            if self.db_conn is None:
                print("Reconnect failed, data not saved.")
                return

        try:
            cursor = self.db_conn.cursor()
            # 注意MySQL-connector使用%s作为占位符
            sql = "INSERT INTO sensor_readings (client_id, temperature) VALUES (%s, %s)"
            val = (client_id, temperature)
            cursor.execute(sql, val)
            self.db_conn.commit()
            print(f"Saved to DB: client_id={client_id}, temperature={temperature}")
            cursor.close()
        except Error as e:
            print(f"Failed to insert data into database: {e}")
            # 发生错误时尝试重连
            self.db_conn.close()
            self.db_conn = self.setup_database()

    def publish_command(self, client_id, command):
        """
        向下游设备发布指令。
        """
        command_topic = COMMAND_TOPIC_FORMAT.format(client_id=client_id)
        command_payload = json.dumps({
            "command": command,
            "timestamp": time.time()
        })

        result = self.client.publish(command_topic, command_payload, qos=1)
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"Successfully published command '{command}' to topic '{command_topic}'")
        else:
            print(f"Failed to publish command. RC: {result.rc}")


if __name__ == '__main__':
    gateway = MqttGateway(MQTT_BROKER_IP, MQTT_BROKER_PORT, MQTT_TIMEOUT)

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Shutting down gateway...")
        if gateway.client:
            gateway.client.loop_stop()
        if gateway.db_conn and gateway.db_conn.is_connected():
            gateway.db_conn.close()
            print("MySQL connection closed.")
        print("Gateway shut down.")
